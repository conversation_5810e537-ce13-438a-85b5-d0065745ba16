## Pronet integration

[Technical Documentation](https://confluence.skywindgroup.com/display/SWI/%5BPronet%5D+%28i%29+Integration+Technical+Documentation)

[Merchant Setup](https://confluence.skywindgroup.com/display/SWI/%5BPronet%5D+Merchant+Setup%3A+GMS)

## Installation

```bash
$ pnpm install
```

## Running the app
### Common
#### Node interpreter
```bash
v22.14.0
```

### Launcher
#### Run command
```bash
npm run start:launcher
```

#### JavaScript file
```bash
src/mainLauncher.ts
```
#### ENV
```bash
INTERNAL_SERVER_PORT=4054
```
### Wallet
#### Run command
```bash
npm run start:wallet
```

#### JavaScript file
```bash
src/mainWallet.ts
```
#### ENV
```bash
INTERNAL_SERVER_PORT=4055
```
### Operator
#### Run command
```bash
npm run start:operator
```

#### JavaScript file
```bash
src/mainOperator.ts
```
#### ENV
```bash
INTERNAL_SERVER_PORT=4054
```
### Mock
#### Run command
```bash
npm run start:mock
```

#### JavaScript file
```bash
src/mainMock.ts
```
#### ENV
```bash
INTERNAL_SERVER_PORT=4056
```
