{"name": "sw-integration-pronet", "version": "1.0.0", "private": true, "scripts": {"clean": "rm -rf ./out", "cleanall": "rm -rf ./out ./pnpm-lock.yaml ./node_modules", "compile": "npm run version && nest build", "format": "prettier --write \"**/*.ts\"", "start:operator": "INTERNAL_SERVER_PORT=4054 env-cmd nest start operator --watch", "start:launcher": "INTERNAL_SERVER_PORT=4054 env-cmd nest start launcher --watch", "start:wallet": "INTERNAL_SERVER_PORT=4055 env-cmd nest start wallet --watch", "start:mock": "INTERNAL_SERVER_PORT=4056 env-cmd nest start mock --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\"", "lint:fix": "eslint \"{src,apps,libs}/**/*.ts\" --fix", "test": "mocha -r ts-node/register --exit src/**/*.spec.ts src/**/**/*.spec.ts", "test:watch": "npm run test -- --watch", "version": "mkdir -p out && echo $npm_package_version $( git log --pretty=format:'%h' -n 1) $(date) > ./out/version"}, "dependencies": {"@fastify/static": "^8.2.0", "@nestjs/common": "11.0.12", "@nestjs/core": "11.0.12", "@nestjs/platform-fastify": "11.0.12", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "11.1.0", "@nestjs/typeorm": "^10.0.2", "@skywind-group/sw-integration-core": "^2.0.11", "@skywind-group/sw-utils": "^2.4.0", "@skywind-group/sw-wallet-adapter-core": "^2.1.4", "@types/jsonwebtoken": "^9.0.9", "agentkeepalive": "^4.6.0", "bole": "^5.0.19", "bole-console": "^0.1.10", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "crypto-js": "^4.2.0", "emitter-listener": "1.1.2", "express-prom-bundle": "^8.0.0", "fastify": "^5.3.3", "jsonwebtoken": "^9.0.2", "kafka-node": "5.0.0", "module-alias": "^2.2.3", "pg": "^8.16.0", "prom-client": "^15.0.0", "typeorm": "^0.3.24"}, "devDependencies": {"@nestjs/cli": "11.0.5", "@nestjs/schematics": "11.0.2", "@nestjs/testing": "11.0.12", "@types/chai": "^5.2.2", "@types/crypto-js": "^4.2.2", "@types/mocha": "^10.0.10", "@types/node": "^22.15.30", "@types/superagent": "^8.1.9", "@typescript-eslint/eslint-plugin": "^8.27.0", "chai": "^5.2.0", "env-cmd": "^10.1.0", "eslint": "^9.23.0", "eslint-plugin-import": "^2.31.0", "mocha": "^11.5.0", "mocha-typescript": "^1.1.17", "prettier": "^3.5.3", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "_moduleAliases": {"@entities": "src/entities", "@errors": "src/errors", "@utils": "src/utils", "@config": "src/config", "@wallet": "src/wallet", "@launcher": "src/launcher", "@operator": "src/operator", "@mock": "src/mock"}, "packageManager": "pnpm@9.6.0"}