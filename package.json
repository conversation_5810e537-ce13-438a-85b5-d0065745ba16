{"name": "@skywind-group/sw-integration-core", "version": "2.0.11", "description": "Common wallet integration module", "author": "<PERSON>", "license": "Skywindgroup", "typings": "./lib/index.d.ts", "main": "./lib/index.js", "packageManager": "npm@10.8.3", "scripts": {"format": "prettier --write \"**/*.ts\"", "lint": "ESLINT_USE_FLAT_CONFIG=true eslint . --fix", "test": "mocha -r ts-node/register --exit src/**/*.spec.ts", "test:coverage": "nyc --reporter=lcov --reporter=lcovonly --reporter=text npm run test", "clean": "rm -rf lib", "compile": "tsc -p tsconfig.json", "compile:watch": "tsc -p tsconfig.json --watch", "build": "npm run clean && npm run compile", "precommit": "lint-staged", "prepare": "husky install", "prepublish:npm": "npm run build", "prepublish": "npm run build", "publish:npm": "npm publish", "publish:local": "npm run compile && npm link", "publish:pack": "npm run compile && npm pack"}, "devDependencies": {"@nestjs/common": "^10.4.17", "@nestjs/core": "^10.4.17", "@nestjs/platform-fastify": "^10.4.17", "@nestjs/testing": "^10.4.17", "@skywind-group/sw-utils": "^2.3.8", "@skywind-group/sw-wallet-adapter-core": "^2.1.3", "@types/chai": "^4.3.20", "@types/chai-as-promised": "^7.1.8", "@types/mocha": "^10.0.10", "@types/node": "^20.17.46", "@types/request-ip": "^0.0.41", "@types/sinon": "^17.0.4", "@types/superagent": "^8.1.9", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "agentkeepalive": "^4.6.0", "auto-changelog": "^2.5.0", "bole": "^5.0.19", "bole-console": "0.1.10", "chai": "^4.5.0", "chai-as-promised": "^7.1.2", "eslint": "^8.57.1", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "~2.31.0", "eslint-plugin-prettier": "^5.4.0", "fastify": "4.29.1", "husky": "8.0.3", "lint-staged": "^15.5.2", "mocha": "^10.8.2", "mocha-typescript": "^1.1.17", "nyc": "^17.1.0", "prettier": "^3.5.3", "prom-client": "15.0.0", "reflect-metadata": "^0.2.2", "sinon": "^17.0.1", "superagent-mocker": "^0.5.2", "supertest": "^7.1.1", "ts-node": "^10.9.2", "typescript": "5.8.3"}, "peerDependencies": {"@skywind-group/sw-utils": "^2.0.50", "@skywind-group/sw-wallet-adapter-core": "^2.0.21", "@nestjs/swagger": "^7.3.1", "deepmerge": "^4.3.1", "request-ip": "^3.3.0", "superagent": "^9.0.2 || ^10.0.0", "uuid": "^9.0.1"}, "uuid": {"optional": true}, "lint-staged": {"*.ts": ["prettier --write"]}, "auto-changelog": {"commitLimit": false, "commitUrl": "https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-integration-core/commits/{id}", "compareUrl": "https://bitbucket.skywindgroup.com/projects/SWB/repos/sw-integration-core/compare/diff?targetBranch=refs%2Ftags%2F{from}&sourceBranch=refs%2Ftags%2F{to}", "issueUrl": "https://jira.skywindgroup.com/browse/{id}", "ignoreCommitPattern": "Pull request|Merge branch|Merge remote-tracking branch|Merge pull request|update changelog.md", "issuePattern": "[A-Z]+-\\d+", "hideCredit": true, "hideEmptyReleases": true}, "repository": {"type": "git", "url": "ssh://******************************:7999/swb/sw-integration-core.git"}}