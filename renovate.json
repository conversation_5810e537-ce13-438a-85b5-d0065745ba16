{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "automerge": false, "internalChecksFilter": "strict", "configMigration": true, "osvVulnerabilityAlerts": true, "patch": {"automerge": true}, "packageRules": [{"groupName": "Dev libraries", "matchDepTypes": ["devDependencies"], "rangeStrategy": "bump", "schedule": ["after 10:00pm on monday", "before 04:00am on tuesday"]}, {"enabled": false, "matchPackageNames": ["chai", "husky", "chai-as-promised"], "matchUpdateTypes": ["major"]}, {"enabled": false, "matchDatasources": ["docker"], "matchUpdateTypes": ["major"]}, {"enabled": false, "matchPackageNames": ["prom-client"], "matchUpdateTypes": ["minor"]}, {"matchDepTypes": ["devDependencies"], "automerge": true, "major": {"automerge": false}}], "schedule": ["before 4am on Friday"]}