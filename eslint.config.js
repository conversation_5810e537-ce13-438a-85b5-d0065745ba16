"use strict";
/** @type {import('eslint').Linter.FlatConfig[]} */
const typescriptEslint = require("@typescript-eslint/eslint-plugin");
const typescriptParser = require("@typescript-eslint/parser");
const importPlugin = require("eslint-plugin-import");
const prettierPlugin = require("eslint-plugin-prettier");

module.exports = [
    {
        ignores: [
            "lib/*",
            "lib/**/*",
            ".husky/*",
            ".husky/**/*",
            "eslint.config.js",
            "node_modules/*",
            "node_modules/**/*",
        ],
        files: ["**/*.ts"],
        languageOptions: {
            ecmaVersion: 2022,
            sourceType: "module",
            parser: typescriptParser,
            parserOptions: {
                tsconfigRootDir: __dirname,
                project: ["./tsconfig.json"],
            },
            globals: {
                node: true,
                jest: true
            },
        },
        plugins: {
            "@typescript-eslint": typescriptEslint,
            import: importPlugin,
            prettier: prettierPlugin,
        },
        rules: {
            "@typescript-eslint/interface-name-prefix": "off",
            "@typescript-eslint/explicit-function-return-type": "off",
            "@typescript-eslint/no-explicit-any": "off",
            "@typescript-eslint/no-use-before-define": "off",
            "@typescript-eslint/no-non-null-assertion": "off",
            "@typescript-eslint/no-unused-vars": "off",
            "quotes": ["error", "double"],
            "object-curly-spacing": ["warn", "always"],
            "semi": ["warn", "always", { "omitLastInOneLineBlock": false}],
            "semi-style": ["warn", "last"],
            "no-extra-semi": ["warn"],
            "semi-spacing": ["warn", { "before": false, "after": true }],
            "comma-dangle": ["error", {
                "arrays": "never",
                "objects": "never",
                "imports": "never",
                "exports": "never",
                "functions": "never"
            }]
        }
    },
]
