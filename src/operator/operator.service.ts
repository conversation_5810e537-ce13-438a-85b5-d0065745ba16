import { HttpStatus, Inject, Injectable } from "@nestjs/common";
import { BaseHttpService, generateInternalToken, SWError } from "@skywind-group/sw-wallet-adapter-core";
import {
    operatorApiErrorStatuses,
    OperatorImageRoundDetailsResponse,
    OperatorRoundDetailsRequest,
    SWRoundDetails,
    operatorApiErrorCodes
} from "@entities/operator.entities";
import { Names } from "@names";
import config from "@config";
import { logging } from "@skywind-group/sw-utils";
import { FastifyReply } from "fastify";

const log = logging.logger("PaymentService");

export interface OperatorService {
    /**
     * Returns url to image contains screen with result state of the round
     * @param request
     * @param response
     */
    getRoundDetails(request: OperatorRoundDetailsRequest, response: FastifyReply): Promise<OperatorImageRoundDetailsResponse>;
}

@Injectable()
export class OperatorService implements OperatorService {

    constructor(@Inject(Names.BaseHttpService) private readonly baseHttpService: BaseHttpService) { }

    public async getRoundDetails(
        request: OperatorRoundDetailsRequest,
        response: FastifyReply
    ): Promise<OperatorImageRoundDetailsResponse> {
        const internalToken = await generateInternalToken({});
        const options = { auth: { internalToken } };

        const merchantType = request.merchantType || config.merchantType;
        const merchantCode = `${merchantType}_${request.trader}__${config.jurisdictionCode}`;
        const historyDetailsUrl = `v1/merchants/${merchantType}/${merchantCode}/history/rounds/${encodeURIComponent(request.roundId)}`;

        let roundDetail;

        try {
            roundDetail = await this.baseHttpService.get<SWRoundDetails>(historyDetailsUrl, {}, options);
        } catch (err) {
            log.info(err, "Round Details");
            if (err instanceof SWError && err.responseStatus === 404) {
                response.status(HttpStatus.NOT_FOUND);
                return {
                    code: operatorApiErrorCodes.NOT_FOUND,
                    status: operatorApiErrorStatuses.NOT_FOUND
                };
            }
            response.status(HttpStatus.BAD_REQUEST);
            return {
                code: operatorApiErrorCodes.UNKNOWN_ERROR,
                status: operatorApiErrorStatuses.UNKNOWN_ERROR
            };
        }

        return {
            code: operatorApiErrorCodes.SUCCESS,
            status: operatorApiErrorStatuses.SUCCESS,
            json: {
                gameId: request.gameId,
                gameName: request.gameId,
                roundId: request.roundId,
                roundDate: roundDetail.firstTs,
                betAmount: roundDetail.bet,
                winAmount: roundDetail.win,
                currency: roundDetail.currency,
            }
        };
    }
}
