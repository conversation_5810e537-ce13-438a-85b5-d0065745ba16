import { Modu<PERSON> } from "@nestjs/common";
import { OperatorController } from "./operator.controller";
import { OperatorService } from "./operator.service";
import { BaseHttpService } from "@skywind-group/sw-wallet-adapter-core";
import config from "@config";
import { Names } from "@names";

@Module({
    controllers: [OperatorController],
    providers: [
        OperatorService,
        {
            provide: Names.BaseHttpService,
            useValue: new BaseHttpService(config.operatorAPIBaseUrl)
        }
    ]
})
export class OperatorModule { }
