import {
    OperatorImageRoundDetailsResponse,
    OperatorRoundDetailsRequest
} from "@entities/operator.entities";
import {
    Controller,
    Get,
    HttpCode,
    HttpStatus,
    Query,
    Res,
    UseFilters,
    ValidationPipe
} from "@nestjs/common";
import { ClientIp, ErrorFilter } from "@skywind-group/sw-integration-core";
import { OperatorService } from "./operator.service";
import { FastifyReply } from "fastify";

@UseFilters(ErrorFilter)
@Controller("replay")
export class OperatorController {
    constructor(private readonly historyService: OperatorService) { }

    @Get("/get")
    @HttpCode(HttpStatus.OK)
    getRoundDetails(@Query(new ValidationPipe({ transform: true })) data: OperatorRoundDetailsRequest,
                    @Res({ passthrough: true }) res: FastifyReply,
                    @ClientIp() ip: string): Promise<OperatorImageRoundDetailsResponse> {
        return this.historyService.getRoundDetails(data, res);
    }
}
