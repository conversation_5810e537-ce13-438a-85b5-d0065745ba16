import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import { stub } from "sinon";
import { expect, use } from "chai";
import "chai-as-promised";
import { ModuleRef } from "@nestjs/core";
import { getHandlerName } from "../utils/handler";
import { HttpGateway } from "../interfaces/http";
import { HttpStartGameServiceAdapter } from "./httpStartGame.adapter";
import { CheckProviderService } from "../utils/checkExistsSupport";
import { OperationForbidden } from "../errors";

// eslint-disable-next-line @typescript-eslint/no-var-requires
use(require("chai-as-promised"));

@suite()
class HttpStartGameServiceAdapterSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async createGameUrl() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("createGameUrl")).returns(true);
        moduleRef.get.withArgs(getHandlerName("createGameUrl")).returns(commitPayment);

        const serviceAdapter = new HttpStartGameServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.createGameUrl(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitPayment);
    }

    @test
    public async createGameTokenData() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitBetPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("createGameTokenData")).returns(true);
        moduleRef.get.withArgs(getHandlerName("createGameTokenData")).returns(commitBetPayment);

        const serviceAdapter = new HttpStartGameServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { gameCode: "sw_al" };
        const res = { url: "http://localhost" };
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.createGameTokenData(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitBetPayment);
    }

    @test
    public async keepAlive() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const commitWinPayment = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("keepAlive")).returns(true);
        moduleRef.get.withArgs(getHandlerName("keepAlive")).returns(commitWinPayment);
        const serviceAdapter = new HttpStartGameServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { operation: "keep-alive" };
        const res = {};
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.keepAlive(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(commitWinPayment);
    }

    @test
    public async loginTerminalPlayer() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const getBalances = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("loginTerminalPlayer")).returns(true);
        moduleRef.get.withArgs(getHandlerName("loginTerminalPlayer")).returns(getBalances);
        const serviceAdapter = new HttpStartGameServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { ticket: "ticket" };
        const res = {};
        gatewayStub.request.resolves(res);
        const result = await serviceAdapter.loginTerminalPlayer(req as any);
        expect(result).eq(res);
        expect(gatewayStub.request.lastCall.args[0]).eq(req);
        expect(gatewayStub.request.lastCall.args[1]).eq(getBalances);
    }

    @test
    public async testForbidden() {
        const gatewayStub = sinon.createStubInstance(HttpGateway);
        const checkProviderStub = sinon.createStubInstance(CheckProviderService);
        const moduleRef = sinon.createStubInstance(ModuleRef);
        (moduleRef as any).get = stub();
        moduleRef.get.withArgs(HttpGateway).returns(gatewayStub);
        moduleRef.get.withArgs(CheckProviderService).returns(checkProviderStub);
        const getBalances = {
            build: () => null,
            parse: () => null
        };
        checkProviderStub.exists.withArgs(getHandlerName("loginTerminalPlayer")).returns(false);
        moduleRef.get.withArgs(getHandlerName("loginTerminalPlayer")).returns(getBalances);
        const serviceAdapter = new HttpStartGameServiceAdapter(moduleRef as any);
        serviceAdapter.onModuleInit();

        const req = { ticket: "ticket" };
        const res = {};
        await expect(serviceAdapter.loginTerminalPlayer(req as any)).to.be.rejectedWith(OperationForbidden);
    }
}
