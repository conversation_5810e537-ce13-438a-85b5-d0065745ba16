import { Test, TestingModule } from "@nestjs/testing";
import { suite, test } from "mocha-typescript";
import * as sinon from "sinon";
import { SinonStubbedInstance } from "sinon";
import { assert, expect } from "chai";
import { Names } from "../names";
import { <PERSON>ttpGate<PERSON>, <PERSON>ttpH<PERSON>ler, HTTPOperatorRequest } from "../interfaces/http";
import { forwardRef, Injectable, Module } from "@nestjs/common";
import { getHandlerName } from "../utils/handler";
import {
    GameLogoutRequest,
    GameLogoutResponse,
    MerchantGameInitRequest,
    MerchantGameTokenData,
    MerchantGameTokenInfo,
    MerchantGameURLInfo,
    MerchantStartGameTokenData
} from "@skywind-group/sw-wallet-adapter-core";
import { StartGameController } from "./startGame.controller";
import { KeepAliveController } from "./keepalive.controller";
import { LoginTerminalController } from "./loginTerminal.controller";
import {
    CreateGameTokenRequest,
    CreateGameTokenSupport,
    CreateGameUrlRequest,
    CreateGameUrlSupport,
    HttpStartGameHandler,
    KeepAliveRequest,
    LoginTerminalPlayerRequest,
    StartGameModule,
    StartGameService
} from "..";
import { StartGameFacade } from "./startGame.facade";
import request = require("superagent");
import { HttpStartGameServiceAdapter } from "./httpStartGame.adapter";

@suite()
class StartModuleSpec {
    public async after() {
        sinon.restore();
    }

    @test
    public async testRegisterWithService() {
        const startGame: SinonStubbedInstance<StartGameService> = sinon.createStubInstance(HttpStartGameServiceAdapter);
        const module: TestingModule = await Test.createTestingModule({
            imports: [StartGameModule.register({ useValue: startGame })]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfaces() {
        const startGame: SinonStubbedInstance<StartGameService> = sinon.createStubInstance(HttpStartGameServiceAdapter);
        const module: TestingModule = await Test.createTestingModule({
            imports: [
                StartGameModule.registerWithConfig({
                    createGameURL: { useValue: startGame },
                    createGameToken: { useValue: startGame },
                    loginTerminal: { useValue: startGame },
                    keepAlive: { useValue: startGame }
                })
            ]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get(Names.CreateGameUrlSupport)).is.not.undefined;
        expect(module.get(Names.CreateGameTokenSupport)).is.not.undefined;
        expect(module.get(Names.KeepAliveSupport)).is.not.undefined;
        expect(module.get(Names.LoginTerminalSupport)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfacesPartial() {
        const startGame: SinonStubbedInstance<StartGameService> = sinon.createStubInstance(HttpStartGameServiceAdapter);

        @HttpStartGameHandler("createGameUrl")
        class CreateGameUrlHandler implements HttpHandler {
            public build(req: any): Promise<HTTPOperatorRequest<any>> {
                return Promise.resolve(undefined);
            }

            public parse(
                response: request.Response,
                req: any,
                httpReq: HTTPOperatorRequest<any> | undefined
            ): Promise<any> {
                return Promise.resolve(undefined);
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                StartGameModule.registerWithConfig({
                    http: {
                        gatewayConfig: {
                            operatorUrl: "http://localhost:8080",
                            keepAlive: {
                                freeSocketKeepAliveTimeout: 2000,
                                maxFreeSockets: 10,
                                socketActiveTTL: 2000
                            }
                        }
                    },
                    createGameToken: { useValue: startGame }
                })
            ]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get(Names.CreateGameTokenSupport)).is.not.undefined;
        expect(module.get(Names.CreateGameUrlSupport)).is.not.undefined;
    }

    @test
    public async testRegisterWithHttpConfig() {
        class KeepAliveHandler implements HttpHandler {
            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                StartGameModule.registerWithConfig({
                    http: {
                        gatewayConfig: {
                            operatorUrl: "http://localhost:8080",
                            keepAlive: {
                                freeSocketKeepAliveTimeout: 2000,
                                maxFreeSockets: 10,
                                socketActiveTTL: 2000
                            }
                        },
                        handlers: {
                            keepAlive: { useClass: KeepAliveHandler }
                        }
                    }
                })
            ]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("keepAlive"))).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("loginTerminalPlayer")));
        assert.throws(() => module.get(getHandlerName("createStartGameToken")));
    }

    @test
    public async testRegisterWithHttpConfigAnAutoInjecting() {
        @Injectable()
        class TestProvider {
        }

        @HttpStartGameHandler("keepAlive")
        class KeepAlivehandler implements HttpHandler {
            constructor(private test: TestProvider) {
            }

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [KeepAlivehandler, TestProvider],
            exports: [KeepAlivehandler, TestProvider]
        })
        class Services {
            constructor() {
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                Services,
                StartGameModule.registerWithConfig(
                    {
                        http: {
                            gatewayConfig: {
                                operatorUrl: "http://localhost:8080",
                                keepAlive: {
                                    freeSocketKeepAliveTimeout: 2000,
                                    maxFreeSockets: 10,
                                    socketActiveTTL: 2000
                                }
                            }
                        }
                    },
                    [Services]
                )
            ]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("keepAlive"))).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("loginTerminalPlayer")));
        assert.throws(() => module.get(getHandlerName("createStartGameToken")));
    }

    @test
    public async testRegisterWithMixedConfig() {
        @Injectable()
        class TestProvider {
        }

        @Injectable()
        class StartGameServiceSupport implements CreateGameUrlSupport, CreateGameTokenSupport {
            constructor(public readonly httpGateway: HttpGateway) {
            }

            public async createGameTokenData(req: CreateGameTokenRequest): Promise<MerchantGameTokenInfo> {
                return undefined;
            }

            public async createGameUrl(req: CreateGameUrlRequest): Promise<MerchantGameURLInfo> {
                return undefined;
            }
        }

        @HttpStartGameHandler("keepAlive")
        class KeepAliveHandler implements HttpHandler {
            constructor(private test: TestProvider) {
            }

            public async build(req: any): Promise<HTTPOperatorRequest> {
                return req;
            }

            public async parse(response: request.Response, req: any): Promise<any> {
                return response;
            }
        }

        @Module({
            providers: [KeepAliveHandler, StartGameServiceSupport, TestProvider],
            exports: [KeepAliveHandler, StartGameServiceSupport, TestProvider],
            imports: [forwardRef(() => startGameModule)]
        })
        class Services {
            constructor() {
            }
        }

        const startGameModule = StartGameModule.registerWithConfig(
            {
                http: {
                    gatewayConfig: {
                        operatorUrl: "http://localhost:8080",
                        keepAlive: {
                            freeSocketKeepAliveTimeout: 2000,
                            maxFreeSockets: 10,
                            socketActiveTTL: 2000
                        }
                    }
                },
                createGameToken: { useClass: StartGameServiceSupport },
                createGameURL: { useClass: StartGameServiceSupport }
            },
            [Services]
        );
        const module: TestingModule = await Test.createTestingModule({
            imports: [Services, startGameModule]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get<HttpGateway>(HttpGateway)).is.not.undefined;
        expect(module.get(Names.HttpGatewayConfig)).is.not.undefined;
        expect(module.get(getHandlerName("keepAlive"))).is.not.undefined;
        expect(module.get(Names.CreateGameUrlSupport)).is.not.undefined;
        expect(module.get(Names.CreateGameTokenSupport)).is.not.undefined;
        expect(module.get<StartGameServiceSupport>(Names.CreateGameUrlSupport).httpGateway).is.not.undefined;
        expect(module.get<StartGameServiceSupport>(Names.CreateGameTokenSupport).httpGateway).is.not.undefined;
        assert.throws(() => module.get(getHandlerName("loginTerminalPlayer")));
        assert.throws(() => module.get(getHandlerName("createStartGameToken")));
    }

    @test
    public async testRegisterWithType() {
        class StartGameServiceMock implements StartGameService {
            public createGameTokenData(
                req: CreateGameTokenRequest<MerchantStartGameTokenData>
            ): Promise<MerchantGameTokenInfo<MerchantGameTokenData>> {
                return Promise.resolve(undefined);
            }

            public createGameUrl(req: CreateGameUrlRequest<MerchantGameInitRequest>): Promise<MerchantGameURLInfo> {
                return Promise.resolve(undefined);
            }

            public keepAlive(req: KeepAliveRequest<MerchantGameTokenData>): Promise<void> {
                return Promise.resolve(undefined);
            }

            public loginTerminalPlayer(req: LoginTerminalPlayerRequest): Promise<any> {
                return Promise.resolve(undefined);
            }

            logoutGame(req: GameLogoutRequest): Promise<GameLogoutResponse> {
                return Promise.resolve(undefined);
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [StartGameModule.register(StartGameServiceMock)]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
    }

    @test
    public async testRegisterWithSubInterfacesAndTypes() {
        class StartGameServiceMock implements StartGameService {
            public createGameTokenData(
                req: CreateGameTokenRequest<MerchantStartGameTokenData>
            ): Promise<MerchantGameTokenInfo<MerchantGameTokenData>> {
                return Promise.resolve(undefined);
            }

            public createGameUrl(req: CreateGameUrlRequest<MerchantGameInitRequest>): Promise<MerchantGameURLInfo> {
                return Promise.resolve(undefined);
            }

            public keepAlive(req: KeepAliveRequest<MerchantGameTokenData>): Promise<void> {
                return Promise.resolve(undefined);
            }

            public loginTerminalPlayer(req: LoginTerminalPlayerRequest): Promise<any> {
                return Promise.resolve(undefined);
            }

            logoutGame(req: GameLogoutRequest): Promise<GameLogoutResponse> {
                return Promise.resolve(undefined);
            }
        }

        const module: TestingModule = await Test.createTestingModule({
            imports: [
                StartGameModule.registerWithConfig({
                    createGameURL: StartGameServiceMock,
                    createGameToken: StartGameServiceMock,
                    loginTerminal: StartGameServiceMock,
                    keepAlive: StartGameServiceMock
                })
            ]
        }).compile();

        expect(module.get<StartGameController>(StartGameController)).is.not.undefined;
        expect(module.get<KeepAliveController>(KeepAliveController)).is.not.undefined;
        expect(module.get<LoginTerminalController>(LoginTerminalController)).is.not.undefined;
        expect(module.get<StartGameFacade>(StartGameFacade)).is.not.undefined;
        expect(module.get(Names.CreateGameUrlSupport)).is.not.undefined;
        expect(module.get(Names.CreateGameTokenSupport)).is.not.undefined;
        expect(module.get(Names.KeepAliveSupport)).is.not.undefined;
        expect(module.get(Names.LoginTerminalSupport)).is.not.undefined;
    }
}
