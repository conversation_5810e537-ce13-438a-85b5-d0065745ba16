import "module-alias/register";
import {
    OperatorError,
    operatorErrorCodes,
    operatorStatusMessages
} from "@entities/operator.entities";
import {
    SWError,
    ConnectionError,
    MerchantAdapterAPIError,
    ERROR_LEVEL
} from "@skywind-group/sw-wallet-adapter-core";

// Custom error classes that don't exist in the current version
export class GeneralError extends SWError {
    constructor(message = "General error") {
        super(500, 5000, message, ERROR_LEVEL.ERROR);
    }
}

export class AuthenticateFailedError extends SWError {
    constructor(message = "Authentication failed") {
        super(401, 4010, message, ERROR_LEVEL.WARN);
    }
}

export class InsufficientBalanceError extends SWError {
    constructor(message = "Insufficient balance") {
        super(400, 4001, message, ERROR_LEVEL.WARN);
    }
}

export class TransactionNotFound extends SWError {
    constructor(message = "Transaction not found") {
        super(404, 4040, message, ERROR_LEVEL.WARN);
    }
}

export class ValidationError extends SWError {
    constructor(message = "Validation error") {
        super(400, 4000, message, ERROR_LEVEL.WARN);
    }
}

export class CannotCompletePayment extends SWError {
    constructor(message: string = "Cannot complete payment!") {
        super(500, 806, message);
    }
}

export function mapOperatorToSWError(
    { code, status }: OperatorError
): SWError {
    switch (code) {
        case operatorErrorCodes.SUCCESS:
            return new GeneralError("Unexpected success code in error handler");

        case operatorErrorCodes.UNKNOWN_ERROR:
        case operatorErrorCodes.INTERNAL_CACHE_ERROR:
        case operatorErrorCodes.DATA_OUT_OF_RANGE:
            return new GeneralError(status || "Unknown error");

        case operatorErrorCodes.UNAUTHORIZED_REQUEST:
            return new AuthenticateFailedError("Unauthorized request - invalid hash");

        case operatorErrorCodes.NOT_INTEGRATED:
            return new AuthenticateFailedError("Vendor not active");

        case operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH:
            return new AuthenticateFailedError("Token was created for another customer");

        case operatorErrorCodes.UNSUPPORTED_API_VERSION:
            return new ValidationError("Unsupported API version");

        case operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED:
            return new ValidationError("Promotion type not supported");

        case operatorErrorCodes.BET_RECORD_NOT_FOUND:
        case operatorErrorCodes.TRANSACTION_NOT_FOUND:
            return new TransactionNotFound(status);

        case operatorErrorCodes.BET_ALREADY_WON:
        case operatorErrorCodes.BET_ALREADY_SETTLED:
            return new OperatorBetAlreadySettledError(status);

        case operatorErrorCodes.AUTHENTICATION_FAILED:
            return new AuthenticateFailedError(status || "Authentication failed");

        case operatorErrorCodes.GAME_NOT_FOUND:
        case operatorErrorCodes.INVALID_GAME:
            return new OperatorGameNotFoundError(status);

        case operatorErrorCodes.BET_LIMIT_REACHED:
        case operatorErrorCodes.LOSS_LIMIT_REACHED:
        case operatorErrorCodes.SESSION_LIMIT_REACHED:
        case operatorErrorCodes.PROFIT_LIMIT_REACHED:
            return new OperatorLimitReachedError(status);

        case operatorErrorCodes.INVALID_CASINO_VENDOR:
            return new AuthenticateFailedError("Invalid casino vendor");

        case operatorErrorCodes.ALL_BET_ARE_OFF:
            return new OperatorBettingOffError(status);

        case operatorErrorCodes.CUSTOMER_NOT_FOUND:
            return new OperatorCustomerNotFoundError(status);

        case operatorErrorCodes.INVALID_CURRENCY:
            return new ValidationError("Invalid currency");

        case operatorErrorCodes.INSUFFICIENT_FUNDS:
            return new InsufficientBalanceError(status || "Insufficient funds");

        case operatorErrorCodes.PLAYER_SUSPENDED:
            return new OperatorPlayerSuspendedError(status);

        case operatorErrorCodes.REQUIRED_FIELD_MISSING:
            return new ValidationError("Required field missing");

        case operatorErrorCodes.TOKEN_NOT_FOUND:
        case operatorErrorCodes.TOKEN_TIMEOUT:
        case operatorErrorCodes.TOKEN_INVALID:
            return new OperatorTokenExpiredError(status);

        case operatorErrorCodes.NEGATIVE_DEPOSIT:
        case operatorErrorCodes.NEGATIVE_WITHDRAWAL:
            return new ValidationError(status || "Invalid amount");

        default:
            return new GeneralError(status || `Unknown operator error: ${code}`);
    }
}

export function makeSWError(
    { code, status }: OperatorError,
    isOfflineRetry?: boolean
): SWError {
    const swError: SWError = mapOperatorToSWError({ code, status });

    if (isOfflineRetry && !refundCondition(swError)) {
        return new CannotCompletePayment(status);
    }

    return swError;
}

// Custom operator-specific errors
export class OperatorBetAlreadySettledError extends SWError {
    constructor(message = "Bet already settled") {
        super(400, 40001, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorGameNotFoundError extends SWError {
    constructor(message = "Game not found") {
        super(404, 40401, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorLimitReachedError extends SWError {
    constructor(message = "Limit reached") {
        super(403, 40301, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorBettingOffError extends SWError {
    constructor(message = "Betting is currently off") {
        super(503, 50301, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorCustomerNotFoundError extends SWError {
    constructor(message = "Customer not found") {
        super(404, 40402, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorPlayerSuspendedError extends SWError {
    constructor(message = "Player is suspended") {
        super(403, 40302, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorTokenExpiredError extends SWError {
    constructor(message = "Token expired or invalid") {
        super(401, 40101, message, ERROR_LEVEL.WARN);
    }
}

export class OperatorHashValidationError extends SWError {
    constructor(message = "Hash validation failed") {
        super(401, 40102, message, ERROR_LEVEL.ERROR);
    }
}

export class OperatorInvalidRequestError extends SWError {
    constructor(message = "Invalid request format") {
        super(400, 40002, message, ERROR_LEVEL.WARN);
    }
}

// Predefined operator errors for testing
export const operatorInsufficientFunds = (): OperatorError => ({
    code: operatorErrorCodes.INSUFFICIENT_FUNDS,
    status: operatorStatusMessages[operatorErrorCodes.INSUFFICIENT_FUNDS]
});

export const operatorTokenExpired = (): OperatorError => ({
    code: operatorErrorCodes.TOKEN_TIMEOUT,
    status: operatorStatusMessages[operatorErrorCodes.TOKEN_TIMEOUT]
});

export const operatorAuthenticationFailed = (): OperatorError => ({
    code: operatorErrorCodes.AUTHENTICATION_FAILED,
    status: operatorStatusMessages[operatorErrorCodes.AUTHENTICATION_FAILED]
});

export const operatorGameNotFound = (): OperatorError => ({
    code: operatorErrorCodes.GAME_NOT_FOUND,
    status: operatorStatusMessages[operatorErrorCodes.GAME_NOT_FOUND]
});

export const operatorBetAlreadySettled = (): OperatorError => ({
    code: operatorErrorCodes.BET_ALREADY_SETTLED,
    status: operatorStatusMessages[operatorErrorCodes.BET_ALREADY_SETTLED]
});

export const operatorPlayerSuspended = (): OperatorError => ({
    code: operatorErrorCodes.PLAYER_SUSPENDED,
    status: operatorStatusMessages[operatorErrorCodes.PLAYER_SUSPENDED]
});

export const operatorUnauthorizedRequest = (): OperatorError => ({
    code: operatorErrorCodes.UNAUTHORIZED_REQUEST,
    status: operatorStatusMessages[operatorErrorCodes.UNAUTHORIZED_REQUEST]
});

// Error condition helpers
export const retryCondition = (err: SWError): boolean => 
    err instanceof ConnectionError ||
    err instanceof GeneralError ||
    err instanceof MerchantAdapterAPIError;

export const rollbackCondition = (err: SWError): boolean =>
    err instanceof ConnectionError ||
    err instanceof GeneralError ||
    err instanceof OperatorBettingOffError ||
    err instanceof OperatorLimitReachedError ||
    err instanceof OperatorPlayerSuspendedError;

export const refundCondition = (err: SWError): boolean =>
    err instanceof OperatorTokenExpiredError ||
    err instanceof OperatorCustomerNotFoundError ||
    err instanceof AuthenticateFailedError;
