import { suite, test, timeout } from "mocha-typescript";
import * as sinon from "sinon";
import { testing } from "@skywind-group/sw-utils";
import * as request from "superagent";
import { HttpGateway, HTTPOperatorRequest } from "../interfaces/http";
import { expect, use } from "chai";
import "chai-as-promised";
import status200 = testing.status200;
import status400 = testing.status400;
import { OperatorTransientError } from "../errors";
import { XmlService } from "@skywind-group/sw-wallet-adapter-core";
import { XmlHttpGateway } from "../interfaces/xmlHttp";

// eslint-disable-next-line @typescript-eslint/no-var-requires
use(require("chai-as-promised"));

@suite()
class HttpGatewaySpec {
    private requestMock: testing.RequestMock;
    private xmlService: XmlService;

    public async before() {
        this.requestMock = testing.requestMock(request);
        this.xmlService = new XmlService();
    }

    public async after() {
        this.requestMock.clearRoutes();
        this.requestMock.unmock(request);
        sinon.restore();
    }

    @test()
    public async testGet() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    expect(req).deep.eq({
                        param1: "value1",
                        param2: "value2"
                    });
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req
                    };
                },
                async parse(
                    response: request.Response,
                    req: { param1: string; param2: string },
                    httpReq: HTTPOperatorRequest
                ): Promise<any> {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    delete httpReq[Symbol.for("HttpGateway_ts")];
                    expect(httpReq).deep.eq({
                        uri: "/test1",
                        method: "get",
                        payload: {
                            param1: "value1",
                            param2: "value2"
                        }
                    });
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: { accept: "application/json" },
            query: { param1: "value1", param2: "value2" },
            method: "GET"
        });
    }

    @test()
    public async testGetWithSlashOnBaseURL() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get/",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: { accept: "application/json" },
            query: { param1: "value1", param2: "value2" },
            method: "GET"
        });
    }

    @test()
    public async testGetWithAdditionalQueryParams() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req,
                        options: {
                            timeout: 2000,
                            headers: {
                                "x-sw-trace-id": "test"
                            },
                            query: {
                                extraParam1: "extra_value1",
                                extraParam2: "extra_value2"
                            }
                        }
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: { accept: "application/json", "x-sw-trace-id": "test" },
            query: {
                param1: "value1",
                param2: "value2",
                extraParam1: "extra_value1",
                extraParam2: "extra_value2"
            },
            method: "GET"
        });
    }

    @test()
    public async testPut() {
        this.requestMock.put("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "put",
                        payload: req
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: { param1: "value1", param2: "value2" },
            headers: { accept: "application/json" },
            query: {},
            method: "PUT"
        });
    }

    @test()
    public async testPost() {
        this.requestMock.post("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "post",
                        payload: req
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: { param1: "value1", param2: "value2" },
            headers: { accept: "application/json" },
            query: {},
            method: "POST"
        });
    }

    @test()
    public async testDelete() {
        (this.requestMock as any).del("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "del",
                        payload: req
                    } as any;
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: { param1: "value1", param2: "value2" },
            headers: { accept: "application/json" },
            query: {},
            method: "DEL"
        });
    }

    @test()
    public async testPostWithExtraQuery() {
        this.requestMock.post("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "post",
                        payload: req,
                        options: {
                            query: { extraParam1: "value1", extraParam2: "value2" }
                        }
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: { param1: "value1", param2: "value2" },
            headers: { accept: "application/json" },
            query: { extraParam1: "value1", extraParam2: "value2" },
            method: "POST"
        });
    }

    @test()
    public async testGetWithSSL() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            },
            ssl: {
                ca: "test_ca",
                cert: "test_cert",
                key: "test_key",
                password: "password"
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: { accept: "application/json" },
            query: { param1: "value1", param2: "value2" },
            method: "GET"
        });
    }

    @test()
    public async testWithJsonContentType() {
        this.requestMock.post("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                build: async (req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> => {
                    return {
                        uri: "/test1",
                        method: "post",
                        contentType: "application/json",
                        payload: req
                    };
                },
                parse: async (response: request.Response, req: { param1: string; param2: string }): Promise<any> => {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: { param1: "value1", param2: "value2" },
            headers: { accept: "application/json" },
            query: {},
            method: "POST"
        });
    }

    @test()
    public async testWithXmlContentType() {
        this.requestMock.post("http://operator_host/do_get/test1", status200("<response><type>ok</type></response>"));
        const gateWay = new XmlHttpGateway(
            {
                operatorUrl: "http://operator_host/do_get",
                keepAlive: {
                    socketActiveTTL: 60000,
                    maxFreeSockets: 100,
                    freeSocketKeepAliveTimeout: 12000
                }
            },
            this.xmlService
        );

        const result = await gateWay.request(
            {
                root: {
                    param1: "value1",
                    param2: "value2"
                }
            },
            {
                build: async (req: { root: { param1: string; param2: string } }): Promise<HTTPOperatorRequest> => {
                    return {
                        uri: "/test1",
                        method: "post",
                        contentType: "application/xml",
                        payload: this.xmlService.convertToXML(req)
                    };
                },
                parse: async (
                    response: request.Response,
                    req: { root: { param1: string; param2: string } }
                ): Promise<any> => {
                    return this.xmlService.convertToObject(response.body);
                }
            }
        );

        expect(result).deep.eq({ response: { type: "ok" } });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: "<root><param1>value1</param1><param2>value2</param2></root>",
            headers: { accept: "application/xml", "content-type": "application/xml" },
            query: {},
            method: "POST"
        });
    }

    @test()
    public async testWithBasicAuth() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        contentType: "application/json",
                        payload: req,
                        options: {
                            username: "basic_auth_user",
                            password: "basic_auth_password"
                        }
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: {
                accept: "application/json",
                authorization: "Basic YmFzaWNfYXV0aF91c2VyOmJhc2ljX2F1dGhfcGFzc3dvcmQ="
            },
            query: { param1: "value1", param2: "value2" },
            method: "GET"
        });
    }

    @test()
    public async testGetWithDefaultOptions() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            },
            defaultOptions: {
                query: {
                    extraParam1: "extraValue1",
                    extraParam2: "extraValue2"
                },
                username: "test_auth_user",
                password: "test_user_pass",
                headers: {
                    "sw-trace-id": "test_id"
                },
                timeout: 2000,
                proxy: "http://localhost:8080"
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: {
                accept: "application/json",
                authorization: "Basic dW5kZWZpbmVkOnVuZGVmaW5lZA==",
                "sw-trace-id": "test_id"
            },
            query: {
                param1: "value1",
                param2: "value2",
                extraParam1: "extraValue1",
                extraParam2: "extraValue2"
            },
            method: "GET"
        });
    }

    @test()
    public async testGetWithOvierrideOfOptions() {
        this.requestMock.get("http://operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            },
            defaultOptions: {
                query: {
                    extraParam1: "extraValue1",
                    extraParam2: "extraValue2"
                },
                username: "test_auth_user",
                password: "test_user_pass",
                headers: {
                    "sw-trace-id": "test_id"
                },
                timeout: 2000,
                proxy: "http://localhost:8080"
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    return {
                        uri: "/test1",
                        method: "get",
                        payload: req,
                        options: {
                            query: {
                                extraParam1: "extraValue11",
                                extraParam2: "extraValue22"
                            },
                            username: "test_auth_user_1",
                            password: "test_user_pass_1",
                            headers: {
                                "sw-trace-id": "test_id_1"
                            },
                            timeout: 3000,
                            proxy: "http://localhost:8081"
                        }
                    };
                },
                async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://operator_host/do_get/test1",
            body: {},
            headers: {
                accept: "application/json",
                authorization: "Basic dGVzdF9hdXRoX3VzZXJfMTp0ZXN0X3VzZXJfcGFzc18x",
                "sw-trace-id": "test_id_1"
            },
            query: {
                param1: "value1",
                param2: "value2",
                extraParam1: "extraValue11",
                extraParam2: "extraValue22"
            },
            method: "GET"
        });
    }

    @test()
    public async testOverrideBaseURL() {
        this.requestMock.get("http://new_operator_host/do_get/test1", status200({ type: "ok" }));
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        const result = await gateWay.request(
            {
                param1: "value1",
                param2: "value2"
            },
            {
                async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                    expect(req).deep.eq({
                        param1: "value1",
                        param2: "value2"
                    });
                    return {
                        baseUrl: "http://new_operator_host/do_get",
                        uri: "/test1",
                        method: "get",
                        payload: req
                    };
                },
                async parse(
                    response: request.Response,
                    req: { param1: string; param2: string },
                    httpReq: HTTPOperatorRequest
                ): Promise<any> {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    delete httpReq[Symbol.for("HttpGateway_ts")];
                    expect(httpReq).deep.eq({
                        baseUrl: "http://new_operator_host/do_get",
                        uri: "/test1",
                        method: "get",
                        payload: {
                            param1: "value1",
                            param2: "value2"
                        }
                    });
                    return response.body;
                }
            }
        );

        expect(result).deep.eq({ type: "ok" });
        expect(this.requestMock.args[0]).deep.eq({
            url: "http://new_operator_host/do_get/test1",
            body: {},
            headers: { accept: "application/json" },
            query: { param1: "value1", param2: "value2" },
            method: "GET"
        });
    }

    @test()
    @timeout(2000)
    public async testRetries() {
        this.requestMock.post("http://operator_host/do_get/test1", status400());
        const gateWay = new HttpGateway({
            operatorUrl: "http://operator_host/do_get",
            keepAlive: {
                socketActiveTTL: 60000,
                maxFreeSockets: 100,
                freeSocketKeepAliveTimeout: 12000
            }
        });

        await expect(
            gateWay.request(
                {
                    param1: "value1",
                    param2: "value2"
                },
                {
                    async build(req: { param1: string; param2: string }): Promise<HTTPOperatorRequest> {
                        return {
                            uri: "/test1",
                            method: "post",
                            payload: req,
                            retry: {
                                maxTimeout: 500,
                                sleep: 10
                            }
                        };
                    },
                    async parse(response: request.Response, req: { param1: string; param2: string }): Promise<any> {
                        if (response.status === 400) {
                            throw new OperatorTransientError();
                        }
                    }
                }
            )
        ).to.be.rejectedWith(OperatorTransientError);

        expect(this.requestMock.args.length).is.greaterThan(1);
    }
}
