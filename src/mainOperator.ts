// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import config from "@config";
import { OperatorModule } from "@operator/operator.module";

bootstrapServer({
    serviceName: "sw-pronet-operator",
    versionFile: "./out/version",
    module: OperatorModule,
    internalPort: config.internalServer.port,
    port: config.server.operatorPort,
    secureKeys: config.securedKeys
});
