import { CallHandler, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { Observable } from "rxjs";
import { tap } from "rxjs/operators";
import { CustomErrorService, RaiseType } from "./customError.service";

@Injectable()
export class CustomErrorInterceptor implements NestInterceptor {
    constructor(protected service: CustomErrorService) {}
    public getHandlerName(request, context): string {
        return context.getHandler().name;
    }

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const action = this.getHandlerName(request, context);
        const merchantId = request.merchant?.merch_id;
        const customerId = request.customer?.cust_id;

        this.service.throwError(merchantId, customerId, action, RaiseType.BEFORE);

        return next.handle().pipe(tap(() => this.service.throwError(merchantId, customerId, action, RaiseType.AFTER)));
    }
}
