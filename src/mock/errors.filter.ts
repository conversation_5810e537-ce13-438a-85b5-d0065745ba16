import { ArgumentsHost, ExceptionFilter } from "@nestjs/common";

export abstract class ExceptionsFilter implements ExceptionFilter {
    public abstract getStatusAndBody(exception: any): [number, any];

    catch(exception: unknown, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();

        const [status, body] = this.getStatusAndBody(exception);

        response.status(status).send(body);
    }
}
