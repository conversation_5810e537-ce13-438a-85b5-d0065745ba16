import { Body, Controller, Get, HttpCode, Param, Patch, Post } from "@nestjs/common";
import { MerchantService } from "./merchant.service";
import { MerchantInfo } from "./merchant";
import { CustomerService } from "./customer.service";
import { Helper } from "./utils/helper";

@Controller("/v1/merchant")
export class MerchantController {
    constructor(
        private readonly merchantService: MerchantService,
        private readonly customerService: CustomerService,
        private readonly helper: Helper
    ) {}

    @Get("/")
    public getAll() {
        return this.merchantService.getAll();
    }

    @Get("/:merch_id")
    public getByMerchantId(@Param("merch_id") merch_id: string) {
        return this.merchantService.getById(merch_id, true);
    }

    @Patch("/:merch_id")
    public updateMerchant(@Param("merch_id") merch_id: string, @Body() merchant: MerchantInfo) {
        return this.merchantService.updateMerchant(merch_id, merchant);
    }

    @Post("/")
    @HttpCode(201)
    public createMerchant(@Body() merchant: MerchantInfo) {
        return this.merchantService.createMerchant(merchant.merch_id, merchant);
    }
}
