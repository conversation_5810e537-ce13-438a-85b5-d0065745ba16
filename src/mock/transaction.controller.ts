import { Controller, Get, Param } from "@nestjs/common";
import { TransactionService } from "./transaction.service";

@Controller("/v1/merchant")
export class TransactionController {
    constructor(public readonly service: TransactionService) {}

    @Get("/:merch_id/customer/:cust_id/transactions")
    public getAllTransactions(@Param("merch_id") merchantId: string, @Param("cust_id") customerId: string) {
        return this.service.getAll(merchantId, customerId);
    }

    @Get("/:merch_id/customer/:cust_id/transactions/:trx_id")
    public getTransactionsById(
        @Param("merch_id") merchantId: string,
        @Param("cust_id") customerId: string,
        @Param("trx_id") trxId: string
    ) {
        return this.service.getAll(merchantId, customerId, trxId);
    }
}
