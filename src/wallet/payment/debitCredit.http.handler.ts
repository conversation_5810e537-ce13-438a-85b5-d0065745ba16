import {
    IntegrationGameTokenData,
    IntegrationPaymentRequest,
    OperatorDebitCreditRequest,
    OperatorDebitCreditResponse
} from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { CommitPaymentRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balance } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import { sumMajorUnits } from "@utils/operator.utils";
import * as superagent from "superagent";

@Injectable()
export class DebitCreditHttpHandler extends BaseHttpHandler
    implements <PERSON>ttpHandler<CommitPaymentRequest<IntegrationGameTokenData>, Balance> {

    public async build({ request, gameTokenData, merchantInfo }: IntegrationPaymentRequest): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest<OperatorDebitCreditRequest>("debit-credit", merchantInfo, {
            payload: {
                customer: gameTokenData.playerCode,
                token: gameTokenData.token,
                gameId: gameTokenData.gameCode,
                amount: this.sanitizeAmount(sumMajorUnits(request.bet)),
                creditAmount: this.sanitizeAmount(sumMajorUnits(request.totalWin)),
                currency: gameTokenData.currency,
                betId: this.generateBetId(request.roundPID),
                trxId: this.generateTransactionId("bet"),
                creditTrxId: this.generateTransactionId("win"),
                tip: false
            },
            retryAvailable: true
        });
    }

    public async parse(response: superagent.Response, req: CommitPaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        const operatorResponse = this.parseHttpResponse<OperatorDebitCreditResponse>(response, req.request.offlineRetry);
        return {
            main: this.sanitizeAmount(operatorResponse.balance + (operatorResponse.bonusBalance || 0))
        };
    }
}
