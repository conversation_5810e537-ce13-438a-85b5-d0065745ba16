import { HttpGatewayConfig } from "@skywind-group/sw-integration-core";

const http: HttpGatewayConfig = {
    operatorUrl: process.env.OPERATOR_BASE_ENGINE_URL || "http://casinoengine.test.operatorgaming.com",
    defaultOptions: {
        timeout: +process.env.OPERATOR_HTTP_TIMEOUT || 10000,
        proxy: process.env.OPERATOR_HTTP_PROXY
    },
    keepAlive: {
        maxFreeSockets: +process.env.OPERATOR_HTTP_KEEP_ALIVE_MAX_FREE_SOCKETS || 100,
        socketActiveTTL: +process.env.OPERATOR_HTTP_KEEP_ALIVE_SOCKET_TTL || 60000,
        freeSocketKeepAliveTimeout: +process.env.OPERATOR_HTTP_KEEP_ALIVE_TIMEOUT || 30000
    },
    ssl: {
        ca: process.env.OPERATOR_SSL_CA,
        key: process.env.OPERATOR_SSL_KEY,
        cert: process.env.OPERATOR_SSL_CERT,
        password: process.env.OPERATOR_SSL_PASSWORD
    }
};

const config = {
    environment: process.env.NODE_ENV || "development",

    isProduction: (): boolean => {
        return config.environment === "production";
    },

    server: {
        walletPort: +process.env.SERVER_WALLET_PORT || 3000,
        launcherPort: +process.env.SERVER_LAUNCHER_PORT || 3001,
        operatorPort: +process.env.SERVER_OPERATOR_PORT || 3002,
        mockPort: +process.env.SERVER_MOCK_PORT || 3003
    },

    internalServer: {
        port: +process.env.INTERNAL_SERVER_PORT || 4054
    },

    operator: {
        retryPolicy: {
            sleep: +process.env.RETRIES_SLEEP_TIMEOUT || 500, // half a sec by default
            maxTimeout: +process.env.RETRIES_MAX_TIMEOUT || 10000 // 10 sec by default
        }
    },

    operatorAPIBaseUrl: process.env.OPERATOR_API_BASE_URL || "http://localhost:3000",

    merchantType: process.env.MERCHANT_TYPE || "operator",
    jurisdictionCode: process.env.DEFAULT_JURISDICTION || "UK",

    currencyUnitMultiplier: +process.env.CURRENCY_UNIT_MULTIPLIER || 100,

    securedKeys: ["password", "username", "privateToken", "sharedKey", "genericSecretKey"],

    http
};

export default config;
