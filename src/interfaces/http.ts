import { keepalive, logging, publicId, RetryConfig } from "@skywind-group/sw-utils";
import * as request from "superagent";
import * as http from "superagent";
import { ServiceProvider } from "./types";
import { Inject, Injectable } from "@nestjs/common";
import { Agent } from "http";
import { Names } from "../names";
import { retry } from "../utils/retry";
import { ConnectionError, getSecuredObjectData, SWError } from "@skywind-group/sw-wallet-adapter-core";
import { URL } from "url";
import config from "../config";
import { ContentType } from "./contentType.enum";
import { ResponseBodyLog } from "./responseBodyLog.interface";
import { RequestLogData } from "./requestLogData.interface";
import { RequestBodyLog } from "./requestBodyLog.interface";

/**
 * SSL configuration
 */
export interface SSLData {
    cert: string;
    key: string;
    password: string;
    ca?: string;
}

/**
 *  Http request parameters
 */
export interface HttpRequestOptions {
    /**
     * Optional BASIC auth username
     */
    username?: string;
    /**
     * Optional BASIC auth password
     */
    password?: string;
    /**
     * Optional proxy url
     */
    proxy?: string;
    /**
     * Optional timeout
     */
    timeout?: number;
    /**
     * Optional auxiliary query fields to be added to url
     */
    query?: {
        [field: string]: string;
    };
    /**
     * Optional headers
     */
    headers?: {
        [field: string]: string;
    };
}

/**
 * Http request description
 */
export interface HTTPOperatorRequest<T = any> {
    /**
     * http method
     */
    method: "get" | "post" | "put" | "delete";

    /**
     *  Ability to override base url to send request to
     */
    baseUrl?: string;
    /**
     * uri to be added to baseURL to send request
     */
    uri?: string;
    /**
     * Content-type, default is application/json
     */
    contentType?: string;
    /**
     * The payload to send.
     * If method is 'get', this will be added as query fields.
     *
     * Otherwise it send as request body. In case of application/json, here could be javascript object.
     * In case of application/xml, here could be xml string
     */
    payload: T;
    /**
     * Additional http request options
     */
    options?: HttpRequestOptions;

    /**
     * Retry policy.
     * It can be standard retry with exponential backoff or custom function.
     */
    retry?: RetryConfig | ((err?: Error) => Promise<boolean>);

    /**
     * Defines conditions for retry
     */
    shouldRetry?: (err: Error) => boolean;

    /**
     * Wrapper for returned error
     */
    wrapError?: (err: Error) => Error;

    /**
     * logMessage, defines a log message for request/response
     */
    logMessage?: string;

    /**
     * logErrorMessage, defines a log message for case of unsuccessful request/response
     */
    logErrorMessage?: string;

    /**
     * Anything that we can pass through build/parse methods of HttpHandler object
     */
    [field: string]: any;
}

/**
 * Build method
 */
export type HttpRequestBuild<REQ = any, PREQ = any> = (req: REQ) => Promise<HTTPOperatorRequest<PREQ>>;

/**
 * Parse method.
 * It can use original request and HttpOperatorRequest object to parse the result depending of the sent request
 *
 * @param response - the superagent object to get status/body/header/etc
 * @param req - original request that as send to build method
 * @param httpReq - the HttOperatorRequest that was build
 */
export type HttpResponseParser<RES = any, REQ = any, PREQ = any> = (
    response: request.Response,
    req: REQ,
    httpReq?: HTTPOperatorRequest<PREQ>
) => Promise<RES>;

/**
 * Http handler
 *
 * The interface is used in case if our integration allows to make simple request/response matching.
 */
export interface HttpHandler<REQ = any, RES = any, PREQ = any> {
    /**
     * Convert request to the http request to be send to operator
     */
    build: HttpRequestBuild<REQ, PREQ>;
    /**
     * Convert response from operator to the response to MAPI
     */
    parse: HttpResponseParser<RES, REQ, PREQ>;
}

/**
 * Configurationof HttpGateway utility to interact with operator.
 */
export interface HttpGatewayConfig {
    /**
     * operator base url
     */
    operatorUrl: string;
    /**
     * Keep alive policy
     */
    keepAlive: keepalive.KeepAliveConfig;
    /**
     * Optional ssl configuration
     */
    ssl?: SSLData;
    /**
     * Default http params to add to any http request to operator
     */
    defaultOptions?: HttpRequestOptions;
    /**
     * Specifies keys whose data will be represented in the logs by asterisks: ***
     */
    logSecureKeys?: string[];
}

/**
 * Http utility to
 *  - build http request
 *  - send http request to operator
 *  - parse the response form operator
 */
@Injectable()
export class HttpGateway {
    private agent: Agent;
    private httpsAgent: Agent;
    private readonly logger = logging.logger("http");
    private static readonly tsSymbol: any = Symbol.for("HttpGateway_ts");

    public constructor(@Inject(Names.HttpGatewayConfig) protected readonly config: HttpGatewayConfig) {
        this.agent = keepalive.createAgent(this.config.keepAlive) as Agent;
        this.httpsAgent = keepalive.createAgent(this.config.keepAlive, true) as Agent;
    }

    public async request<REQ, RES>(request: REQ, handler: HttpHandler<REQ, RES>): Promise<RES> {
        const httpRequest = await handler.build(request);
        return retry(
            httpRequest.retry,
            () => this.doRequest(request, httpRequest, handler.parse.bind(handler)),
            httpRequest.shouldRetry,
            httpRequest.wrapError
        );
    }

    private getAgent(httpReq: HTTPOperatorRequest) {
        const baseUrl = httpReq.baseUrl || this.config.operatorUrl;
        return baseUrl.startsWith("https") ? this.httpsAgent : this.agent;
    }

    private doRequest<REQ>(request: REQ, httpReq: HTTPOperatorRequest, parser: HttpResponseParser) {
        const options: Partial<HttpRequestOptions> = { ...this.config.defaultOptions, ...httpReq.options };
        const url = this.getFullUrl(httpReq.uri, httpReq.baseUrl || this.config.operatorUrl);

        httpReq[HttpGateway.tsSymbol] = Date.now();
        const req = http[httpReq.method](url)
            .agent(this.getAgent(httpReq))
            .accept(httpReq.contentType || ContentType.ApplicationJson)
            .timeout(options?.timeout);
        this.prepareRequest(req, httpReq, options);
        return this.process(req, request, httpReq, parser);
    }

    protected prepareRequest(
        req: request.SuperAgentRequest,
        httpReq: HTTPOperatorRequest,
        options: Partial<HttpRequestOptions>
    ): void {
        if (options?.username && options?.password) {
            req.auth(httpReq.options?.username, httpReq.options?.password);
        }

        if (httpReq.method == "get") {
            req.query({ ...(options?.query || {}), ...httpReq.payload });
        } else {
            req.query(options?.query).send(httpReq.payload);
        }

        const headers = options?.headers || {};

        for (const headerKey of Object.keys(headers)) {
            let headerValue = headers[headerKey];
            if (headerKey === "x-sw-t-round-id" && !isNaN(parseInt(headerValue))) {
                headerValue = publicId.instance.encode(headerValue);
            }
            req.set(headerKey, headerValue);
        }

        if (this.config.ssl) {
            if (this.config.ssl.cert) {
                req.cert(Buffer.from(this.config.ssl.cert));
            }

            if (this.config.ssl.key) {
                req.key(Buffer.from(this.config.ssl.key));
            }

            if (this.config.ssl.ca) {
                req.ca(Buffer.from(this.config.ssl.ca));
            }
        }

        const proxy = options.proxy || config.defaultHttpProxy;
        if (proxy) {
            (req as any).proxy(proxy);
        }

        if (options?.timeout) {
            req.timeout(options.timeout);
        }
    }

    private async process<REQ>(
        sendRequestPromise: http.SuperAgentRequest,
        request: REQ,
        httpReq: HTTPOperatorRequest,
        parser: HttpResponseParser
    ): Promise<any> {
        this.logRequest(httpReq.method, sendRequestPromise.url, httpReq.payload, httpReq.options);
        try {
            const res: http.Response = await sendRequestPromise;
            return this.processResponse(res, request, httpReq, parser);
        } catch (err) {
            return this.processResponse(err.response, request, httpReq, parser, err);
        }
    }

    private async processResponse<REQ>(
        response: http.Response,
        req: REQ,
        httpReq: HTTPOperatorRequest,
        responseParser: HttpResponseParser<any, any>,
        error?: Error
    ): Promise<any> {
        const requestData = this.getHttpReqLogData(httpReq);
        const responseData = response ? this.prepareResponseBodyLog(response) : null;
        if (error) {
            this.logger.error(
                {
                    error: logging.errAsObject(error as any),
                    url: httpReq.uri,
                    request: requestData,
                    responseStatus: response ? response.status : null,
                    response: responseData,
                    requestTime: this.getRequestTime(httpReq[HttpGateway.tsSymbol])
                },
                httpReq.logErrorMessage || "merchant api request/response"
            );
        } else {
            this.logger.info(
                {
                    url: httpReq.uri,
                    request: requestData,
                    responseStatus: response ? response.status : null,
                    response: responseData,
                    error: this.getSecuredData(error),
                    requestTime: this.getRequestTime(httpReq[HttpGateway.tsSymbol])
                },
                httpReq.logMessage || "merchant api request/response"
            );
        }

        if (error && !response) {
            return Promise.reject(new ConnectionError(this.config.operatorUrl, error as SWError));
        }

        return responseParser(response, req, httpReq);
    }

    private logRequest(method: string, url: string, data: any, options: HttpRequestOptions = {}) {
        const securedData = this.getSecuredData(data);
        const reqBodyLog = this.prepareRequestBodyLog(securedData);
        const reqLogData: any = {
            method,
            url,
            proxy: options.proxy,
            ...reqBodyLog
        };
        this.logger.info({ request: reqLogData }, `${method} %s`, url);
    }

    protected prepareRequestBodyLog(data: any): RequestBodyLog {
        return { data: data };
    }

    protected prepareResponseBodyLog(response: http.Response): ResponseBodyLog {
        return { data: response.body };
    }

    protected prepareHttpRequestLogData(httpReq: HTTPOperatorRequest): RequestBodyLog {
        return { data: this.getSecuredData(httpReq.payload) };
    }

    protected getSecuredData(data: any): any {
        return getSecuredObjectData(data, this.config.logSecureKeys);
    }

    private getRequestTime(ts: number): number {
        return (Date.now() - ts) / 1000;
    }

    /**
     * crutch for some operators (which are integrated via seamless adapter)
     * some operators need to put a question mark at the end of the baseUrl
     * baseUrl -> merchant.params.serverUrl
     * this is unusual behavior for url, due to this need this additional function
     * https://jira.skywindgroup.com/browse/SWS-35990
     */
    private isBaseUrlIncludeQuestionMark(baseURL: string) {
        const isBaseUrlIncludeQuestion = baseURL.includes("?");
        if (isBaseUrlIncludeQuestion) {
            // trim last slash
            const baseUrlWithoutLastSlash = baseURL.endsWith("/") ? baseURL.slice(0, -1) : baseURL;
            // find last index of slash before question mark
            const lastSlashIndex = baseUrlWithoutLastSlash.lastIndexOf("/");

            return {
                isBaseUrlIncludeQuestion,
                baseUrlWithoutQuestionMark: baseUrlWithoutLastSlash.slice(0, lastSlashIndex),
                partWithQuestionMark: baseUrlWithoutLastSlash.slice(lastSlashIndex)
            };
        }

        return {
            isBaseUrlIncludeQuestion,
            baseUrlWithoutQuestionMark: baseURL,
            partWithQuestionMark: ""
        };
    }

    private getFullUrl(url: string, baseUrl: string) {
        const {
            isBaseUrlIncludeQuestion,
            baseUrlWithoutQuestionMark,
            partWithQuestionMark
        } = this.isBaseUrlIncludeQuestionMark(baseUrl);

        const preparedBaseUrl = baseUrlWithoutQuestionMark.endsWith("/")
            ? baseUrlWithoutQuestionMark
            : baseUrlWithoutQuestionMark + "/";
        const preparedUrl = url.startsWith("/") ? url.substring(1) : url;
        let resultUrl = new URL(preparedUrl, preparedBaseUrl).toString();

        if (isBaseUrlIncludeQuestion) {
            resultUrl = resultUrl.replace(
                baseUrlWithoutQuestionMark,
                `${baseUrlWithoutQuestionMark}${partWithQuestionMark}`
            );
        }
        return resultUrl;
    }

    private getHttpReqLogData(httpReq: HTTPOperatorRequest): RequestLogData {
        const contentType = httpReq.contentType;
        const httpReqData = this.prepareHttpRequestLogData(httpReq);
        return {
            method: httpReq.method,
            uri: httpReq.uri,
            type: contentType,
            ...httpReqData
        };
    }
}

/**
 * Http handlers configuration
 */
export interface HttpHandlersConfig<T> {
    /**
     * Http gateway configuration
     */
    gatewayConfig: HttpGatewayConfig;
    /**
     * Providers of the methods
     */
    handlers?: {
        [field in keyof Partial<T>]: ServiceProvider<HttpHandler>;
    };
}
