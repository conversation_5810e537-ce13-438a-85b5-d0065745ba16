/**
 * Should be the first Lines
 */
import { createInternalServer, createServer } from "./base";

import { SwaggerModule } from "@nestjs/swagger";
import { BootstrapConfig, BootstrapMockConfig } from "./bootstrap.config";
import { logging } from "@skywind-group/sw-utils";
import { NestFastifyApplication } from "@nestjs/platform-fastify";
/**
 *  Startup function, include configuration of the internal server
 */
export async function bootstrapServer(bootstrapConfig: BootstrapConfig): Promise<NestFastifyApplication> {
    bootstrapConfig = { doNotStartApplication: false, ...bootstrapConfig };
    logging.setRootLogger(bootstrapConfig.serviceName);
    const app = await createServer(bootstrapConfig);
    await createInternalServer({
        port: bootstrapConfig.internalPort,
        versionFile: bootstrapConfig.versionFile,
        serviceName: bootstrapConfig.serviceName
    });
    return app;
}

export async function bootstrapMock(bootstrapConfig: BootstrapMockConfig): Promise<NestFastifyApplication> {
    bootstrapConfig = { doNotStartApplication: false, ...bootstrapConfig };
    logging.setRootLogger(bootstrapConfig.serviceName);
    await createInternalServer({
        port: bootstrapConfig.internalPort,
        versionFile: bootstrapConfig.versionFile,
        serviceName: bootstrapConfig.serviceName
    });

    const app = await createServer(bootstrapConfig);

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const swagger = require("../../resources/swagger.json");
    swagger.info.title = `${bootstrapConfig.serviceName.toUpperCase()} - ${swagger.info.title}`;
    swagger.info.description = `${bootstrapConfig.serviceName.toUpperCase()} - ${swagger.info.description}`;
    swagger.parameters.action.enum = bootstrapConfig.actions;
    SwaggerModule.setup("/docs", app, swagger);
    app.enableCors({ origin: "*" });

    return app;
}
