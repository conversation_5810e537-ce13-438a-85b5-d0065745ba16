/**
 * Should be the first Lines
 */
import { NestFactory } from "@nestjs/core";
import { FastifyAdapter, NestFastifyApplication } from "@nestjs/platform-fastify";
import { LoggerAdapter } from "../utils/logging.adapter";
import { LoggingInterceptor } from "../utils/logging.interceptor";
import { InternalModule, InternalModuleConfig } from "../internal/internal.module";
import { BootstrapModule } from "./bootstrap.module";
import { ServerInfo } from "../internal/serverInfo.provider";
import { Names } from "../names";
import { BootstrapConfig } from "./bootstrap.config";
import baseConfig from "../config";

export async function createServer(config: BootstrapConfig): Promise<NestFastifyApplication> {
    const loggerAdapter = new LoggerAdapter("app");
    const app = await NestFactory.create<NestFastifyApplication>(
        BootstrapModule.register(config.module, {
            serviceName: config.serviceName,
            versionFile: config.versionFile
        }),
        new FastifyAdapter(),
        {
            logger: loggerAdapter
        }
    );
    app.useGlobalInterceptors(
        new LoggingInterceptor(config.secureKeys || baseConfig.logging.defaultSecureKeys) as any);

    if (!config.doNotStartApplication) {
        await app.listen(config.port, "0.0.0.0");
    }
    const serverInfo: ServerInfo = app.get(Names.ServerInfoProvider);
    loggerAdapter.log(`Start server ${config.serviceName}, port: ${config.port} version: ${serverInfo.version}`);

    return app;
}

export async function createInternalServer(config: InternalModuleConfig): Promise<NestFastifyApplication> {
    const loggerAdapter = new LoggerAdapter("internal-app");
    const app = await NestFactory.create<NestFastifyApplication>(
        InternalModule.register(config),
        new FastifyAdapter(),
        {
            logger: loggerAdapter
        }
    );
    await app.listen(config.port || 4004, "0.0.0.0");
    loggerAdapter.log(`Start internal server ${config.serviceName}, port: ${config.port}`);
    return app;
}
