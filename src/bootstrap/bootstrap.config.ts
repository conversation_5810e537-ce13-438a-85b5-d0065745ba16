import { Type } from "@nestjs/common";

/**
 * Configuration of the server
 */
export interface BootstrapConfig {
    /**
     * Module to start
     */
    module: Type<any>;
    /**
     * The name of the service to return in the health check endpoint
     */
    serviceName: string;
    /**
     * The version file location
     */
    versionFile: string;
    /**
     * Internal server port
     */
    internalPort?: number;
    /**
     * Server port
     */
    port: number;
    /**
     * Secure Keys
     */
    secureKeys?: string[]

    /**
     * Default is false for backwards compatibility
     * the call to app.listen(...) should be at the very end
     */
    doNotStartApplication?: boolean;
}

export interface BootstrapMockConfig extends BootstrapConfig {
    actions: string[];
}
