import { MerchantGameInitRequest, MerchantGameTokenData, MerchantGameURLInfo, MerchantStartGameTokenData, PaymentRequest } from "@skywind-group/sw-wallet-adapter-core";
import { CommitBonusPaymentRequest } from "@skywind-group/sw-integration-core";
import { IsNotEmpty, IsOptional, ValidateIf } from "class-validator";

export class OperatorGameLaunchRequest {
    @IsOptional()
    @IsNotEmpty()
    merchantType?: string; // Optional parameter used for testing

    @IsOptional()
    @IsNotEmpty()
    merchantCode?: string; // Optional parameter used for testing

    @IsNotEmpty()
    trader: string;

    @IsNotEmpty()
    gameId: string;

    @IsNotEmpty()
    demo?: boolean;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    token?: string;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    customer?: string;

    @ValidateIf(o => o.demo === false)
    @IsNotEmpty()
    currency?: string;

    @IsNotEmpty()
    lang: string;

    @IsOptional()
    @IsNotEmpty()
    country: string;

    @IsNotEmpty()
    platform: string;

    @IsOptional()
    lobby?: string;
}

export class OperatorRoundDetailsRequest {
    @IsOptional()
    @IsNotEmpty()
    merchantType?: string; // Optional parameter used for testing

    @IsNotEmpty()
    customer: string;

    @IsNotEmpty()
    roundId: string;

    @IsNotEmpty()
    gameId: string;

    @IsNotEmpty()
    lang: string;

    @IsNotEmpty()
    trader: string;
}

export interface IntegrationInitRequest extends MerchantGameInitRequest {
    customer?: string;
    country: string;
    currency: string;
    platform: "desktop" | "mobile";
}

export interface IntegrationStartGameTokenData extends MerchantStartGameTokenData {
    token?: string;
}

export interface IntegrationMerchantGameURLInfo {
    urlParams: MerchantGameURLInfo["urlParams"];
    tokenData: IntegrationStartGameTokenData;
}

export interface IntegrationGameTokenData extends MerchantGameTokenData {
    token: string;
}

export interface IntegrationPaymentRequest extends CommitBonusPaymentRequest<IntegrationGameTokenData> {
    request: PaymentRequest;
    operatorRoundId?: string;
}

export interface OperatorFreespinData {
    freespinRef: string;
    requested?: boolean;
    remainingRounds?: number;
    totalWinnings?: number;
}

export interface OperatorPromoData {
    promoType: OperatorPromoType;
    promoRef: string;
    freeSpinData?: OperatorFreespinData;
}

export enum OperatorPromoType {
    FSW = "FSW", // freespin
    JPW = "JPW", // jackpot
    CB = "CB",   // cashBack
    TW = "TW",   // tournament win
    RW = "RW",   // reward
    REW = "REW", // red envelope win
    CDW = "CDW", // cash drop win
    RB = "RB"    // rakeBack
}

export interface OperatorBaseRequest {
    customer: string;
    token: string;
}

export type OperatorAuthRequest = OperatorBaseRequest

export interface OperatorPaymentRequest extends OperatorBaseRequest {
    gameId: string;
    amount: number;
    currency: string;
    betId: string;
    trxId: string;
}

export interface OperatorDebitRequest extends OperatorPaymentRequest {
    tip?: boolean;
}

export interface OperatorCreditRequest extends OperatorPaymentRequest {
    freespin?: OperatorFreespinData;
}

export interface OperatorDebitCreditRequest extends OperatorDebitRequest {
    creditAmount: number;
    creditTrxId: string;
}

export interface OperatorRollbackRequest extends OperatorBaseRequest {
    gameId: string;
    trxId: string;
}

export interface OperatorPromoRequest extends OperatorPaymentRequest {
    promo: OperatorPromoData;
}

export interface OperatorBaseResponse {
    balance: number;
    bonusBalance: number;
    code: number;
    currency: string;
    status: string;
}

export interface OperatorAuthResponse extends OperatorBaseResponse {
    traderId?: number;
}

export interface OperatorPaymentResponse extends OperatorBaseResponse {
    trxId: string;
}

export type OperatorDebitResponse = OperatorPaymentResponse;

export type OperatorCreditResponse = OperatorPaymentResponse;

export interface OperatorDebitCreditResponse extends OperatorPaymentResponse {
    creditTrxId?: number;
}

export type OperatorRollbackResponse = OperatorPaymentResponse;

export type OperatorPromoResponse = OperatorPaymentResponse;

export interface OperatorImageRoundDetailsBaseResponse {
    code: number;
    status: string;
}

export interface OperatorImageRoundDetailsResponse extends OperatorImageRoundDetailsBaseResponse {
    json?: {
        gameId: string;
        gameName: string;
        roundId: string;
        roundDate: string;
        betAmount: number;
        winAmount: number;
        currency: string;
    }
}

export interface OperatorError {
    code: number;
    status: string;
}

export interface SWRoundDetails {
    playerCode: string;
    device?: string;
    gameCode: string;
    currency: string;
    isTest: boolean;
    bet: number;
    win: number;
    revenue?: number;
    totalEvents?: number;
    balanceBefore?: number;
    balanceAfter?: number;
    broken?: boolean;
    firstTs?: string;
    ts?: string;
    finished?: boolean;
}

export interface GameHistoryVisualisation {
    imageUrl: string;
    ttl?: number;
}

export const operatorErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    UNAUTHORIZED_REQUEST: -2,
    NOT_INTEGRATED: -3,
    TOKEN_CUSTOMER_MISMATCH: -4,
    UNSUPPORTED_API_VERSION: -5,
    INTERNAL_CACHE_ERROR: -6,
    PROMOTION_TYPE_NOT_SUPPORTED: -7,
    BET_RECORD_NOT_FOUND: -20120,
    BET_ALREADY_WON: -20112,
    AUTHENTICATION_FAILED: -20101,
    GAME_NOT_FOUND: -20130,
    BET_LIMIT_REACHED: -20201,
    LOSS_LIMIT_REACHED: -20202,
    SESSION_LIMIT_REACHED: -20203,
    PROFIT_LIMIT_REACHED: -20204,
    INVALID_CASINO_VENDOR: -20301,
    ALL_BET_ARE_OFF: -20302,
    INVALID_GAME: -20303,
    CUSTOMER_NOT_FOUND: -20304,
    INVALID_CURRENCY: -20305,
    INSUFFICIENT_FUNDS: -20306,
    PLAYER_SUSPENDED: -20307,
    REQUIRED_FIELD_MISSING: -20308,
    DATA_OUT_OF_RANGE: -20309,
    BET_ALREADY_SETTLED: -20310,
    TOKEN_NOT_FOUND: -20316,
    TOKEN_TIMEOUT: -20311,
    TOKEN_INVALID: -20312,
    TRANSACTION_NOT_FOUND: -20313,
    NEGATIVE_DEPOSIT: -20314,
    NEGATIVE_WITHDRAWAL: -20315
} as const;

export const operatorApiErrorCodes = {
    SUCCESS: 0,
    UNKNOWN_ERROR: -1,
    NOT_FOUND: -2,
} as const;

export const operatorApiErrorStatuses = {
    SUCCESS: "SUCCESS",
    UNKNOWN_ERROR: "UNKNOWN_ERROR",
    NOT_FOUND: "NOT_FOUND",
} as const;

export const operatorStatusMessages = {
    [operatorErrorCodes.SUCCESS]: "Request was successful",
    [operatorErrorCodes.UNKNOWN_ERROR]: "General error that does not fall into any of the other categories",
    [operatorErrorCodes.INTERNAL_CACHE_ERROR]: "One of our caches failed to get data from db",
    [operatorErrorCodes.DATA_OUT_OF_RANGE]: "Problem on our side",
    [operatorErrorCodes.UNAUTHORIZED_REQUEST]: "Invalid hash",
    [operatorErrorCodes.NOT_INTEGRATED]: "Vendor not active",
    [operatorErrorCodes.TOKEN_CUSTOMER_MISMATCH]: "The received token was created for another customer",
    [operatorErrorCodes.UNSUPPORTED_API_VERSION]: "The operation you are trying to call is not supported",
    [operatorErrorCodes.PROMOTION_TYPE_NOT_SUPPORTED]: "Sent promotion is not supported in this version",
    [operatorErrorCodes.BET_RECORD_NOT_FOUND]: "Record was not found in our system",
    [operatorErrorCodes.TRANSACTION_NOT_FOUND]: "Transaction not found",
    [operatorErrorCodes.BET_ALREADY_WON]: "Bet was already settled",
    [operatorErrorCodes.BET_ALREADY_SETTLED]: "Bet was already settled",
    [operatorErrorCodes.AUTHENTICATION_FAILED]: "Problem when authenticating",
    [operatorErrorCodes.GAME_NOT_FOUND]: "There is a miss-match between the game id in our system and your system",
    [operatorErrorCodes.INVALID_GAME]: "There is a miss-match between the game id in our system and your system",
    [operatorErrorCodes.BET_LIMIT_REACHED]: "The player can no longer place bets",
    [operatorErrorCodes.LOSS_LIMIT_REACHED]: "The player can no longer place bets",
    [operatorErrorCodes.SESSION_LIMIT_REACHED]: "The player can no longer place bets",
    [operatorErrorCodes.PROFIT_LIMIT_REACHED]: "The player can no longer place bets",
    [operatorErrorCodes.INVALID_CASINO_VENDOR]: "The game provider was not found on our side",
    [operatorErrorCodes.ALL_BET_ARE_OFF]: "This means betting is off. This is usually when we have maintenance of which you will be informed prior",
    [operatorErrorCodes.CUSTOMER_NOT_FOUND]: "The player was not found in our system",
    [operatorErrorCodes.INVALID_CURRENCY]: "The request contains a currency that we don't support(have configured)",
    [operatorErrorCodes.INSUFFICIENT_FUNDS]: "The player does not have enough balance to place a bet",
    [operatorErrorCodes.PLAYER_SUSPENDED]: "The player is suspended",
    [operatorErrorCodes.REQUIRED_FIELD_MISSING]: "Forgot to send a parameter in request",
    [operatorErrorCodes.TOKEN_NOT_FOUND]: "Customer with that token not found",
    [operatorErrorCodes.TOKEN_TIMEOUT]: "The token has timed-out",
    [operatorErrorCodes.TOKEN_INVALID]: "The token has timed-out",
    [operatorErrorCodes.NEGATIVE_DEPOSIT]: "The deposit value is negative",
    [operatorErrorCodes.NEGATIVE_WITHDRAWAL]: "Negative withdrawal"
} as const;
