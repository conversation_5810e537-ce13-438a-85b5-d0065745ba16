import { Controller, Get, Header } from "@nestjs/common";
import { measures } from "@skywind-group/sw-utils";
import { Readable } from "stream";

@Controller()
export class MetricsController {
    @Get("/metrics")
    @Header("Content-Type", "text/plain")
    public getMetricsStream(): Promise<Readable> {
        measures.measureProvider.setTransaction("Prometheus metrics");
        return measures.measureProvider.getMeasuresStream();
    }
}
