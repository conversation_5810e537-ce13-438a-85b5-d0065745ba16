import { DynamicModule, Module } from "@nestjs/common";
import { ServerInfoProvider } from "./serverInfo.provider";
import { InfoController } from "./info.controller";

export interface ServerInfoModuleConfig {
    serviceName: string;
    versionFile: string;
}

/**
 * Internal module to handle metrics/versions/health endpoints
 */
@Module({
    imports: [],
    controllers: [InfoController]
})
export class ServerInfoModule {
    public static register(config: ServerInfoModuleConfig): DynamicModule {
        return {
            module: ServerInfoModule,
            providers: [ServerInfoProvider(config.serviceName, config.versionFile)]
        };
    }
}
